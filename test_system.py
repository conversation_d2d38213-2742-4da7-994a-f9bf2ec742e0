#!/usr/bin/env python3
"""
系统测试脚本

用于验证多模态融合系统的各个组件是否正常工作
"""

import os
import sys
import torch
import numpy as np
from torch_geometric.data import Data
import warnings
warnings.filterwarnings('ignore')

def test_imports():
    """测试所有必要的导入"""
    print("测试导入...")
    
    try:
        # 测试PyTorch相关
        import torch
        import torch.nn as nn
        import torch.optim as optim
        print(f"✓ PyTorch {torch.__version__}")
        
        # 测试PyTorch Geometric
        import torch_geometric
        from torch_geometric.nn import GCNConv, global_mean_pool
        from torch_geometric.data import Data, DataLoader
        print(f"✓ PyTorch Geometric {torch_geometric.__version__}")
        
        # 测试医学图像处理
        import nibabel as nib
        print(f"✓ NiBabel {nib.__version__}")
        
        # 测试MONAI
        import monai
        from monai.transforms import Compose, ScaleIntensity, Resize, ToTensor
        print(f"✓ MONAI {monai.__version__}")
        
        # 测试科学计算
        import numpy as np
        import pandas as pd
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import accuracy_score, f1_score
        print(f"✓ NumPy {np.__version__}")
        print(f"✓ Pandas {pd.__version__}")
        print("✓ Scikit-learn")
        
        # 测试可视化
        import matplotlib.pyplot as plt
        import seaborn as sns
        print("✓ Matplotlib & Seaborn")
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_models():
    """测试模型创建"""
    print("\n测试模型创建...")
    
    try:
        # 测试3D ResNet
        from resnet3D_model import resnet3d
        
        structural_model = resnet3d(
            model_name="resnet34",
            in_channels=1,
            num_classes=2,
            feature_extract=True
        )
        print("✓ 3D ResNet模型创建成功")
        
        # 测试GCN
        from gcn_model import GCN
        
        functional_model = GCN(
            num_node_features=16,
            hidden_channels=64,
            num_classes=2,
            feature_extract=True
        )
        print("✓ GCN模型创建成功")
        
        # 测试多模态融合模型
        from gcn_model import create_multimodal_model
        
        multimodal_model = create_multimodal_model(
            structural_model_name="resnet34",
            gcn_hidden_channels=64,
            gcn_num_node_features=16,
            fusion_dim=256,
            device='cpu'
        )
        print("✓ 多模态融合模型创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型创建失败: {e}")
        return False

def test_data_loading():
    """测试数据加载"""
    print("\n测试数据加载...")
    
    try:
        # 检查数据目录
        required_dirs = [
            'data_smri/HC',
            'data_smri/MCI',
            'data_fmri/selected_timeseries/HC',
            'data_fmri/selected_timeseries/MCI',
            'data_fmri/subject_matrices/HC',
            'data_fmri/subject_matrices/MCI'
        ]
        
        missing_dirs = []
        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                missing_dirs.append(dir_path)
        
        if missing_dirs:
            print(f"✗ 缺少数据目录: {missing_dirs}")
            print("  请确保数据目录结构正确")
            return False
        
        print("✓ 数据目录结构正确")
        
        # 测试数据集创建
        from multimodal_dataset import MultiModalDataset
        from monai.transforms import Compose, ScaleIntensity, Resize, ToTensor
        
        def add_channel_dim(x):
            return x[None, ...]
        
        transform = Compose([
            add_channel_dim,
            ScaleIntensity(minv=0.0, maxv=1.0),
            Resize(spatial_size=(64, 128, 128)),
            ToTensor()
        ])
        
        dataset = MultiModalDataset(
            structural_transform=transform,
            connectivity_threshold=0.3
        )
        
        if len(dataset) > 0:
            print(f"✓ 数据集创建成功，包含 {len(dataset)} 个样本")
            
            # 测试数据加载
            sample = dataset[0]
            print(f"✓ 数据样本加载成功")
            print(f"  - 结构数据形状: {sample['structural_data'].shape}")
            print(f"  - 功能数据节点数: {sample['functional_data'].x.shape[0]}")
            print(f"  - 功能数据特征维度: {sample['functional_data'].x.shape[1]}")
            print(f"  - 标签: {sample['label']}")
            
            return True
        else:
            print("✗ 数据集为空，请检查数据文件")
            return False
        
    except Exception as e:
        print(f"✗ 数据加载测试失败: {e}")
        return False

def test_forward_pass():
    """测试前向传播"""
    print("\n测试前向传播...")
    
    try:
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"使用设备: {device}")
        
        # 创建模型
        from gcn_model import create_multimodal_model
        
        model = create_multimodal_model(
            structural_model_name="resnet34",
            gcn_hidden_channels=64,
            gcn_num_node_features=16,
            fusion_dim=256,
            device=device
        ).to(device)
        
        # 创建测试数据
        batch_size = 2
        
        # 结构数据 (batch_size, channels, depth, height, width)
        structural_data = torch.randn(batch_size, 1, 64, 128, 128).to(device)
        
        # 功能数据 (图数据)
        num_nodes_per_graph = 22
        total_nodes = batch_size * num_nodes_per_graph
        
        # 节点特征
        x = torch.randn(total_nodes, 16).to(device)
        
        # 边索引 (为每个图创建一些随机边)
        edge_indices = []
        for i in range(batch_size):
            start_node = i * num_nodes_per_graph
            end_node = (i + 1) * num_nodes_per_graph
            
            # 创建一些随机边
            num_edges = 50
            src = torch.randint(start_node, end_node, (num_edges,))
            dst = torch.randint(start_node, end_node, (num_edges,))
            edge_index = torch.stack([src, dst], dim=0)
            edge_indices.append(edge_index)
        
        edge_index = torch.cat(edge_indices, dim=1).to(device)
        edge_attr = torch.randn(edge_index.shape[1], 1).to(device)
        
        # 批次信息
        batch = torch.cat([torch.full((num_nodes_per_graph,), i) for i in range(batch_size)]).to(device)
        
        # 创建功能数据对象
        functional_data = Data(x=x, edge_index=edge_index, edge_attr=edge_attr, batch=batch)
        
        # 前向传播
        model.eval()
        with torch.no_grad():
            outputs = model(structural_data, functional_data)
        
        print(f"✓ 前向传播成功")
        print(f"  - 输入结构数据形状: {structural_data.shape}")
        print(f"  - 输入功能数据节点数: {x.shape[0]}")
        print(f"  - 输出形状: {outputs.shape}")
        print(f"  - 输出范围: [{outputs.min().item():.4f}, {outputs.max().item():.4f}]")
        
        return True
        
    except Exception as e:
        print(f"✗ 前向传播测试失败: {e}")
        return False

def test_training_components():
    """测试训练组件"""
    print("\n测试训练组件...")
    
    try:
        # 测试损失函数
        import torch.nn as nn
        criterion = nn.CrossEntropyLoss()
        print("✓ 损失函数创建成功")
        
        # 测试优化器
        import torch.optim as optim
        from gcn_model import create_multimodal_model
        
        model = create_multimodal_model(
            structural_model_name="resnet34",
            gcn_hidden_channels=64,
            gcn_num_node_features=16,
            fusion_dim=256,
            device='cpu'
        )
        
        optimizer = optim.Adam(model.parameters(), lr=0.0001, weight_decay=0.01)
        print("✓ 优化器创建成功")
        
        # 测试学习率调度器
        scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=100, eta_min=1e-6)
        print("✓ 学习率调度器创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 训练组件测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("多模态融合系统测试")
    print("=" * 60)
    
    tests = [
        ("导入测试", test_imports),
        ("模型创建测试", test_models),
        ("数据加载测试", test_data_loading),
        ("前向传播测试", test_forward_pass),
        ("训练组件测试", test_training_components)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"测试失败: {test_name}")
        except Exception as e:
            print(f"测试异常: {test_name} - {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！系统准备就绪。")
        print("\n可以运行以下命令开始实验:")
        print("python run_multimodal_experiment.py --mode full")
    else:
        print("✗ 部分测试失败，请检查环境配置和数据准备。")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
