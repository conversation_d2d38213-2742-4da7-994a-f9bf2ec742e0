# 使用指南

## 🚀 快速开始

### 1. 系统测试
在开始实验之前，建议先运行系统测试：

```bash
python test_system.py
```

这将检查：
- 所有依赖包是否正确安装
- 模型是否能正常创建
- 数据是否能正常加载
- 前向传播是否正常工作

### 2. 运行完整实验

```bash
# 运行完整实验（推荐）
python run_multimodal_experiment.py --mode full

# 使用自定义配置
python run_multimodal_experiment.py --mode full --config config_example.json
```

## 📋 实验模式

### 1. 完整实验模式 (full)
```bash
python run_multimodal_experiment.py --mode full
```
包含：
- 多模态融合模型训练
- 单分支消融实验
- 可解释性分析
- 综合报告生成

### 2. 仅多模态训练 (multimodal)
```bash
python run_multimodal_experiment.py --mode multimodal
```
仅训练多模态融合模型

### 3. 仅消融实验 (ablation)
```bash
python run_multimodal_experiment.py --mode ablation
```
仅运行单分支消融实验

### 4. 仅可解释性分析 (analysis)
```bash
python run_multimodal_experiment.py --mode analysis
```
仅运行可解释性分析（需要已训练的模型）

## 🔧 单独运行各模块

### 训练多模态融合模型
```bash
python train_multimodal_model.py
```

### 运行消融实验
```bash
python ablation_study.py
```

### 测试数据加载
```bash
python multimodal_dataset.py
```

### 测试模型创建
```bash
python gcn_model.py
```

## 📁 实验结果结构

运行完整实验后，会生成如下目录结构：

```
experiment_YYYYMMDD_HHMMSS/
├── models/
│   ├── best_multimodal_model.pth      # 最佳多模态模型
│   ├── best_structural_only_model.pth # 最佳结构分支模型
│   └── best_functional_only_model.pth # 最佳功能分支模型
├── results/
│   ├── test_results.json              # 测试结果
│   ├── training_history.json          # 训练历史
│   └── ablation_study_results.json    # 消融实验结果
├── figures/
│   ├── test_confusion_matrix.png       # 测试集混淆矩阵
│   └── ablation_study_comparison.png   # 消融实验对比图
├── interpretability/
│   ├── attention_distribution.png      # 注意力权重分布
│   ├── structural_features_tsne.png    # 结构特征t-SNE
│   ├── functional_features_tsne.png    # 功能特征t-SNE
│   ├── fused_features_tsne.png         # 融合特征t-SNE
│   ├── extracted_features.npz         # 提取的特征
│   └── interpretability_report.md     # 可解释性报告
├── logs/                              # 日志文件
├── config.json                        # 实验配置
└── comprehensive_report.md            # 综合实验报告
```

## ⚙️ 配置文件说明

### 创建自定义配置
复制 `config_example.json` 并修改：

```bash
cp config_example.json my_config.json
# 编辑 my_config.json
python run_multimodal_experiment.py --mode full --config my_config.json
```

### 主要配置项

#### 模型配置
```json
{
  "model": {
    "structural_model": "resnet34",        // ResNet架构
    "gcn_hidden_channels": 64,             // GCN隐藏层维度
    "fusion_dim": 256,                     // 融合特征维度
    "dropout_rate": 0.5                    // Dropout率
  }
}
```

#### 训练配置
```json
{
  "training": {
    "batch_size": 8,                       // 批次大小
    "learning_rate": 0.0001,               // 学习率
    "epochs": 100,                         // 训练轮数
    "weight_decay": 0.01                   // 权重衰减
  }
}
```

#### 数据配置
```json
{
  "data": {
    "connectivity_threshold": 0.3,         // 功能连接阈值
    "train_ratio": 0.8,                    // 训练集比例
    "val_ratio": 0.1,                      // 验证集比例
    "test_ratio": 0.1                      // 测试集比例
  }
}
```

## 🎯 性能优化建议

### 1. 硬件优化
```bash
# 检查GPU可用性
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"

# 如果GPU内存不足，减小batch_size
# 在config.json中修改：
"training": {
  "batch_size": 4  // 从8减少到4
}
```

### 2. 训练优化
- **使用预训练权重**：确保 `resnet_18_23dataset.pth` 文件存在
- **调整学习率**：如果收敛慢，可以增加学习率到 `0.001`
- **早停策略**：设置 `early_stopping_patience` 避免过拟合

### 3. 数据优化
- **连接阈值**：调整 `connectivity_threshold` (0.2-0.5)
- **数据增强**：在配置中启用/禁用数据增强
- **特征维度**：调整 `gcn_num_node_features` 和 `gcn_hidden_channels`

## 🔍 结果解读

### 1. 性能指标
- **准确率 (Accuracy)**：正确分类的样本比例
- **F1分数 (F1 Score)**：精确率和召回率的调和平均
- **混淆矩阵**：详细的分类结果

### 2. 消融实验结果
- **仅结构分支**：仅使用灰质体积图的性能
- **仅功能分支**：仅使用功能连接的性能
- **多模态融合**：结合两种模态的性能

### 3. 可解释性分析
- **注意力权重**：模型对不同特征的关注程度
- **特征可视化**：t-SNE和PCA降维可视化
- **特征重要性**：统计显著性分析

## 🚨 常见问题

### 1. 内存不足
```bash
# 解决方案：
# 1. 减小batch_size
# 2. 使用CPU训练（较慢）
# 3. 减小模型尺寸
```

### 2. 数据加载错误
```bash
# 检查数据路径
ls data_smri/HC/
ls data_fmri/selected_timeseries/HC/

# 检查文件格式
python -c "import nibabel as nib; print('NiBabel working')"
```

### 3. 模型不收敛
```bash
# 解决方案：
# 1. 检查学习率（可能过大或过小）
# 2. 检查数据质量
# 3. 增加训练轮数
# 4. 调整模型架构
```

### 4. 预训练权重加载失败
```bash
# 检查文件是否存在
ls -la resnet_18_23dataset.pth

# 如果没有预训练权重，设置为None
"pretrained_path": null
```

## 📊 实验建议

### 1. 首次运行
1. 先运行系统测试：`python test_system.py`
2. 使用默认配置运行小规模实验
3. 检查结果是否合理

### 2. 参数调优
1. 基于首次结果调整超参数
2. 运行多次实验确保稳定性
3. 记录最佳配置

### 3. 深入分析
1. 仔细分析可解释性结果
2. 对比不同模态的贡献
3. 分析失败案例

## 📞 获取帮助

如果遇到问题：
1. 查看生成的日志文件
2. 检查错误信息
3. 参考README.md中的故障排除部分
4. 提交GitHub Issue（如果适用）

---

**祝您实验顺利！** 🎉
