#!/usr/bin/env python3
"""
多模态融合模型完整实验脚本

这个脚本整合了：
1. 多模态融合模型训练
2. 单分支消融实验
3. 可解释性分析
4. 结果对比和可视化

使用方法:
python run_multimodal_experiment.py [--mode MODE] [--config CONFIG_FILE]

MODE选项:
- full: 运行完整实验（默认）
- multimodal: 仅运行多模态融合模型
- ablation: 仅运行消融实验
- analysis: 仅运行可解释性分析
"""

import os
import sys
import argparse
import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def create_experiment_directory():
    """创建实验目录"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    exp_dir = f"experiment_{timestamp}"
    os.makedirs(exp_dir, exist_ok=True)
    
    # 创建子目录
    subdirs = ['models', 'results', 'figures', 'logs', 'interpretability']
    for subdir in subdirs:
        os.makedirs(os.path.join(exp_dir, subdir), exist_ok=True)
    
    return exp_dir

def load_config(config_file=None):
    """加载配置文件"""
    default_config = {
        'experiment': {
            'name': 'multimodal_hc_mci_classification',
            'description': '多模态HC vs MCI分类实验',
            'random_seed': 42
        },
        'data': {
            'structural_data_dir': 'data_smri',
            'functional_data_dir': 'data_fmri',
            'train_ratio': 0.8,
            'val_ratio': 0.1,
            'test_ratio': 0.1,
            'connectivity_threshold': 0.3
        },
        'model': {
            'structural_model': 'resnet18',
            'pretrained_path': 'resnet_18_23dataset.pth',
            'gcn_hidden_channels': 64,
            'gcn_num_node_features': 16,
            'fusion_dim': 256,
            'dropout_rate': 0.5,
            'num_classes': 2
        },
        'training': {
            'batch_size': 8,
            'learning_rate': 0.0001,
            'weight_decay': 0.01,
            'epochs': 20,
            'early_stopping_patience': 20,
            'save_best_model': True
        },
        'ablation': {
            'run_structural_only': True,
            'run_functional_only': True,
            'epochs': 10  # 消融实验使用较少的epoch
        },
        'interpretability': {
            'run_analysis': True,
            'save_features': True,
            'generate_report': True
        }
    }
    
    if config_file and os.path.exists(config_file):
        with open(config_file, 'r') as f:
            user_config = json.load(f)
        
        # 递归更新配置
        def update_config(default, user):
            for key, value in user.items():
                if key in default and isinstance(default[key], dict) and isinstance(value, dict):
                    update_config(default[key], value)
                else:
                    default[key] = value
        
        update_config(default_config, user_config)
    
    return default_config

def run_multimodal_training(config, exp_dir):
    """运行多模态融合模型训练"""
    print("=" * 60)
    print("开始多模态融合模型训练")
    print("=" * 60)

    # 保持在原始目录，不切换工作目录
    original_dir = os.getcwd()

    try:
        # 导入训练模块
        import train_multimodal_model

        # 设置输出目录环境变量，让训练脚本知道输出到哪里
        os.environ['EXPERIMENT_OUTPUT_DIR'] = exp_dir

        # 传递配置参数到训练脚本
        train_config = {
            'batch_size': config['training']['batch_size'],
            'learning_rate': config['training']['learning_rate'],
            'weight_decay': config['training']['weight_decay'],
            'epochs': config['training']['epochs'],
            'structural_model': config['model']['structural_model'],
            'pretrained_path': config['model']['pretrained_path'],
            'gcn_hidden_channels': config['model']['gcn_hidden_channels'],
            'gcn_num_node_features': config['model']['gcn_num_node_features'],
            'fusion_dim': config['model']['fusion_dim'],
            'dropout_rate': config['model']['dropout_rate'],
            'connectivity_threshold': config['data']['connectivity_threshold'],
            'early_stopping_patience': config['training']['early_stopping_patience'],
            'save_best_model': config['training']['save_best_model']
        }

        # 运行训练
        train_multimodal_model.main_with_config(train_config)

        # 移动生成的文件到相应目录
        files_to_move = {
            'best_multimodal_model.pth': os.path.join(exp_dir, 'models/'),
            'test_results.json': os.path.join(exp_dir, 'results/'),
            'training_history.json': os.path.join(exp_dir, 'results/'),
            'test_confusion_matrix.png': os.path.join(exp_dir, 'figures/'),
            'interpretability_results': os.path.join(exp_dir, 'interpretability/')
        }

        for src, dst in files_to_move.items():
            if os.path.exists(src):
                if os.path.isdir(src):
                    import shutil
                    dst_path = os.path.join(dst, os.path.basename(src))
                    if os.path.exists(dst_path):
                        shutil.rmtree(dst_path)
                    shutil.move(src, dst_path)
                else:
                    dst_path = os.path.join(dst, os.path.basename(src))
                    if os.path.exists(dst_path):
                        os.remove(dst_path)
                    shutil.move(src, dst_path)

        print("多模态融合模型训练完成")
        return True

    except Exception as e:
        print(f"多模态融合模型训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理环境变量
        if 'EXPERIMENT_OUTPUT_DIR' in os.environ:
            del os.environ['EXPERIMENT_OUTPUT_DIR']

def run_ablation_study(config, exp_dir):
    """运行消融实验"""
    print("=" * 60)
    print("开始消融实验")
    print("=" * 60)
    
    original_dir = os.getcwd()
    os.chdir(exp_dir)
    
    try:
        sys.path.append(original_dir)
        from ablation_study import run_ablation_study
        
        # 运行消融实验
        results = run_ablation_study()
        
        # 移动生成的文件
        files_to_move = {
            'ablation_study_results.json': 'results/',
            'ablation_study_comparison.png': 'figures/',
            'best_structural_only_model.pth': 'models/',
            'best_functional_only_model.pth': 'models/'
        }
        
        for src, dst in files_to_move.items():
            if os.path.exists(src):
                os.rename(src, dst + os.path.basename(src))
        
        print("消融实验完成")
        return results
        
    except Exception as e:
        print(f"消融实验失败: {e}")
        return None
    finally:
        os.chdir(original_dir)

def generate_comprehensive_report(config, exp_dir):
    """生成综合实验报告"""
    print("=" * 60)
    print("生成综合实验报告")
    print("=" * 60)
    
    report_path = os.path.join(exp_dir, 'comprehensive_report.md')
    
    try:
        # 读取结果文件
        results_files = {
            'multimodal': os.path.join(exp_dir, 'results', 'test_results.json'),
            'training_history': os.path.join(exp_dir, 'results', 'training_history.json'),
            'ablation': os.path.join(exp_dir, 'results', 'ablation_study_results.json')
        }
        
        results = {}
        for key, file_path in results_files.items():
            if os.path.exists(file_path):
                with open(file_path, 'r') as f:
                    results[key] = json.load(f)
        
        # 生成报告
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 多模态HC vs MCI分类实验综合报告\n\n")
            
            # 实验概述
            f.write("## 1. 实验概述\n\n")
            f.write(f"- **实验名称**: {config['experiment']['name']}\n")
            f.write(f"- **实验描述**: {config['experiment']['description']}\n")
            f.write(f"- **实验时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"- **随机种子**: {config['experiment']['random_seed']}\n\n")
            
            # 数据集信息
            f.write("## 2. 数据集信息\n\n")
            f.write(f"- **结构数据**: 灰质体积图 (18个脑区)\n")
            f.write(f"- **功能数据**: 功能连接矩阵和时间序列 (22个脑区)\n")
            f.write(f"- **数据划分**: 训练集{config['data']['train_ratio']*100:.0f}%, ")
            f.write(f"验证集{config['data']['val_ratio']*100:.0f}%, ")
            f.write(f"测试集{config['data']['test_ratio']*100:.0f}%\n\n")
            
            # 模型配置
            f.write("## 3. 模型配置\n\n")
            f.write(f"- **结构分支**: {config['model']['structural_model'].upper()}\n")
            f.write(f"- **功能分支**: GCN (隐藏层维度: {config['model']['gcn_hidden_channels']})\n")
            f.write(f"- **融合方式**: 双线性融合 + 注意力机制\n")
            f.write(f"- **融合特征维度**: {config['model']['fusion_dim']}\n\n")
            
            # 实验结果
            f.write("## 4. 实验结果\n\n")
            
            if 'multimodal' in results:
                multimodal_acc = results['multimodal']['overall_metrics']['accuracy']
                multimodal_f1 = results['multimodal']['overall_metrics']['f1_score']
                f.write(f"### 4.1 多模态融合模型\n")
                f.write(f"- **测试准确率**: {multimodal_acc:.4f}\n")
                f.write(f"- **测试F1分数**: {multimodal_f1:.4f}\n\n")
            
            if 'ablation' in results:
                f.write(f"### 4.2 消融实验结果\n")
                structural_acc = results['ablation']['structural_only']['accuracy']
                structural_f1 = results['ablation']['structural_only']['f1_score']
                functional_acc = results['ablation']['functional_only']['accuracy']
                functional_f1 = results['ablation']['functional_only']['f1_score']
                
                f.write(f"- **仅结构分支**: 准确率 {structural_acc:.4f}, F1 {structural_f1:.4f}\n")
                f.write(f"- **仅功能分支**: 准确率 {functional_acc:.4f}, F1 {functional_f1:.4f}\n")
                
                if 'multimodal' in results:
                    f.write(f"- **多模态融合**: 准确率 {multimodal_acc:.4f}, F1 {multimodal_f1:.4f}\n\n")
                    
                    # 性能提升分析
                    f.write(f"### 4.3 性能提升分析\n")
                    struct_improvement = (multimodal_acc - structural_acc) / structural_acc * 100
                    func_improvement = (multimodal_acc - functional_acc) / functional_acc * 100
                    f.write(f"- **相比仅结构分支提升**: {struct_improvement:+.2f}%\n")
                    f.write(f"- **相比仅功能分支提升**: {func_improvement:+.2f}%\n\n")
            
            # 训练过程
            if 'training_history' in results:
                f.write("## 5. 训练过程\n\n")
                history = results['training_history']
                f.write(f"- **训练轮数**: {len(history['train_losses'])}\n")
                f.write(f"- **最佳验证准确率**: {history['best_val_acc']:.4f}\n")
                f.write(f"- **最终训练损失**: {history['train_losses'][-1]:.4f}\n")
                f.write(f"- **最终验证损失**: {history['val_losses'][-1]:.4f}\n\n")
            
            # 可解释性分析
            interpretability_report = os.path.join(exp_dir, 'interpretability', 'interpretability_report.md')
            if os.path.exists(interpretability_report):
                f.write("## 6. 可解释性分析\n\n")
                f.write("详细的可解释性分析结果请参见: `interpretability/interpretability_report.md`\n\n")
            
            # 文件结构
            f.write("## 7. 实验文件结构\n\n")
            f.write("```\n")
            f.write("experiment_YYYYMMDD_HHMMSS/\n")
            f.write("├── models/                    # 训练好的模型\n")
            f.write("├── results/                   # 实验结果\n")
            f.write("├── figures/                   # 图表\n")
            f.write("├── interpretability/          # 可解释性分析\n")
            f.write("├── logs/                      # 日志文件\n")
            f.write("└── comprehensive_report.md    # 综合报告\n")
            f.write("```\n\n")
            
            # 结论
            f.write("## 8. 结论\n\n")
            if 'multimodal' in results and 'ablation' in results:
                f.write("本实验成功实现了结构MRI和功能MRI的多模态融合，")
                f.write("通过双线性融合和注意力机制有效整合了两种模态的信息。")
                f.write("实验结果表明，多模态融合模型在HC vs MCI分类任务上")
                f.write("相比单模态模型有显著的性能提升。\n\n")
            
            f.write("---\n")
            f.write(f"*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n")
        
        print(f"综合报告已生成: {report_path}")
        return True
        
    except Exception as e:
        print(f"生成综合报告失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='多模态融合模型实验')
    parser.add_argument('--mode', choices=['full', 'multimodal', 'ablation', 'analysis'], 
                       default='full', help='实验模式')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--exp_dir', type=str, help='实验目录（如果不指定则自动创建）')
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 创建实验目录
    if args.exp_dir:
        exp_dir = args.exp_dir
        os.makedirs(exp_dir, exist_ok=True)
    else:
        exp_dir = create_experiment_directory()
    
    print(f"实验目录: {exp_dir}")
    
    # 保存配置
    with open(os.path.join(exp_dir, 'config.json'), 'w') as f:
        json.dump(config, f, indent=2)
    
    # 根据模式运行实验
    success = True
    
    if args.mode in ['full', 'multimodal']:
        success &= run_multimodal_training(config, exp_dir)
    
    if args.mode in ['full', 'ablation']:
        run_ablation_study(config, exp_dir)
    
    if args.mode == 'full':
        generate_comprehensive_report(config, exp_dir)
    
    if success:
        print("\n" + "=" * 60)
        print("实验完成！")
        print(f"结果保存在: {exp_dir}")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("实验过程中出现错误，请检查日志")
        print("=" * 60)

if __name__ == "__main__":
    main()
