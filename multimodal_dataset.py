import os
import glob
import numpy as np
import pandas as pd
import torch
import nibabel as nib
from torch.utils.data import Dataset
from torch_geometric.data import Data
from sklearn.model_selection import train_test_split
from monai.transforms import (
    Compose, ScaleIntensity, RandRotate, RandFlip,
    RandZoom, Resize, ToTensor
)
import random
from scipy import stats

def set_seed(seed=42):
    """设置随机种子确保可复现性"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def add_channel_dim(x):
    """为图像数据添加通道维度"""
    return x[None, ...]

def extract_subject_id_from_path(file_path, data_type='structural'):
    """
    从文件路径中提取被试ID
    
    Args:
        file_path: 文件路径
        data_type: 数据类型 ('structural' 或 'functional')
    
    Returns:
        被试ID
    """
    filename = os.path.basename(file_path)
    
    if data_type == 'structural':
        # 从 ROI_smwp101001_MPRAGE_SENSE_20111226120623_301.nii 提取 101001
        # 或从 ROI_smwp110001_WIP_Sagittal_3D_Accelerated_MPRAGE_SENSE_20180213145206_201.nii 提取 110001
        if filename.startswith('ROI_smwp'):
            parts = filename.split('_')
            if len(parts) >= 2:
                subject_id = parts[1].replace('smwp', '')
                # 转换为标准格式
                if len(subject_id) == 6:
                    if subject_id.startswith('10'):
                        # 101001 -> 01001
                        subject_id = '0' + subject_id[2:]
                    elif subject_id.startswith('11'):
                        # 110001 -> 10001
                        subject_id = subject_id[1:]
                    elif subject_id.startswith('20'):
                        # 201001 -> 01001
                        subject_id = '0' + subject_id[2:]
                    elif subject_id.startswith('21'):
                        # 210001 -> 10001
                        subject_id = subject_id[1:]
                    elif subject_id.startswith('10'):
                        # 104001 -> 04001
                        subject_id = '0' + subject_id[2:]
                return subject_id
    elif data_type == 'functional':
        # 从 ROISignals_01001_selected.txt 或 ROICorrelation_FisherZ_01001_submatrix.txt 提取 01001
        if 'ROISignals_' in filename:
            return filename.split('ROISignals_')[1].split('_')[0]
        elif 'ROICorrelation_FisherZ_' in filename:
            return filename.split('ROICorrelation_FisherZ_')[1].split('_')[0]
    
    return None

def load_functional_node_features(subject_id, label):
    """
    加载功能数据的节点特征
    
    Args:
        subject_id: 被试ID
        label: 标签 (0=HC, 1=MCI)
    
    Returns:
        节点特征矩阵
    """
    # 根据标签确定路径
    if label == 0:  # HC
        feature_file = f'data_fmri/selected_timeseries/HC/ROISignals_{subject_id}_selected.txt'
    else:  # MCI
        feature_file = f'data_fmri/selected_timeseries/MCI/ROISignals_{subject_id}_selected.txt'
    
    if not os.path.exists(feature_file):
        return None
    
    try:
        # 加载时间序列数据
        signals = np.loadtxt(feature_file)
        
        if signals.ndim == 1:
            signals = signals.reshape(-1, 1)
        
        # 计算扩展的节点特征
        mean = np.mean(signals, axis=0).reshape(-1, 1)
        std = np.std(signals, axis=0).reshape(-1, 1)
        min_val = np.min(signals, axis=0).reshape(-1, 1)
        max_val = np.max(signals, axis=0).reshape(-1, 1)
        median = np.median(signals, axis=0).reshape(-1, 1)
        
        # 高阶统计特征
        skewness = stats.skew(signals, axis=0).reshape(-1, 1)
        kurtosis = stats.kurtosis(signals, axis=0).reshape(-1, 1)
        
        # 变异性特征
        var = np.var(signals, axis=0).reshape(-1, 1)
        range_val = (max_val - min_val)
        iqr = np.percentile(signals, 75, axis=0).reshape(-1, 1) - np.percentile(signals, 25, axis=0).reshape(-1, 1)
        
        # 能量特征
        energy = np.sum(signals**2, axis=0).reshape(-1, 1)
        rms = np.sqrt(np.mean(signals**2, axis=0)).reshape(-1, 1)
        
        # 时间域特征
        zero_crossings = np.sum(np.diff(np.sign(signals - mean.T), axis=0) != 0, axis=0).reshape(-1, 1)
        
        # 自相关特征
        autocorr = np.array([np.corrcoef(signals[:-1, i], signals[1:, i])[0, 1] if len(np.unique(signals[:, i])) > 1 else 0
                           for i in range(signals.shape[1])]).reshape(-1, 1)
        
        # 频域特征
        fft_signals = np.fft.fft(signals, axis=0)
        fft_magnitude = np.abs(fft_signals)
        dominant_freq_energy = np.max(fft_magnitude, axis=0).reshape(-1, 1)
        spectral_centroid = np.sum(fft_magnitude * np.arange(fft_magnitude.shape[0]).reshape(-1, 1), axis=0) / (np.sum(fft_magnitude, axis=0) + 1e-8)
        spectral_centroid = spectral_centroid.reshape(-1, 1)
        
        # 组合所有特征
        features = np.hstack([
            mean, std, min_val, max_val, median,
            skewness, kurtosis, var, range_val, iqr,
            energy, rms, zero_crossings,
            autocorr, dominant_freq_energy, spectral_centroid
        ])
        
        # 处理NaN值
        features = np.nan_to_num(features, nan=0.0, posinf=0.0, neginf=0.0)
        
        return features
        
    except Exception as e:
        print(f"加载节点特征失败 {feature_file}: {e}")
        return None

def load_functional_connectivity_matrix(subject_id, label):
    """
    加载功能连接矩阵
    
    Args:
        subject_id: 被试ID
        label: 标签 (0=HC, 1=MCI)
    
    Returns:
        功能连接矩阵
    """
    # 根据标签确定路径
    if label == 0:  # HC
        matrix_file = f'data_fmri/subject_matrices/HC/ROICorrelation_FisherZ_{subject_id}_submatrix.txt'
    else:  # MCI
        matrix_file = f'data_fmri/subject_matrices/MCI/ROICorrelation_FisherZ_{subject_id}_submatrix.txt'
    
    if not os.path.exists(matrix_file):
        return None
    
    try:
        # 加载功能连接矩阵
        connectivity_matrix = np.loadtxt(matrix_file)
        return connectivity_matrix
    except Exception as e:
        print(f"加载功能连接矩阵失败 {matrix_file}: {e}")
        return None

def create_graph_from_connectivity(connectivity_matrix, node_features, threshold=0.3):
    """
    从功能连接矩阵创建图数据
    
    Args:
        connectivity_matrix: 功能连接矩阵
        node_features: 节点特征
        threshold: 连接阈值
    
    Returns:
        PyTorch Geometric Data对象
    """
    # 应用阈值，只保留强连接
    adj_matrix = np.abs(connectivity_matrix) > threshold
    
    # 获取边索引
    edge_indices = np.where(adj_matrix)
    edge_index = np.vstack([edge_indices[0], edge_indices[1]])
    
    # 获取边权重
    edge_weights = connectivity_matrix[edge_indices]
    
    # 转换为PyTorch张量
    x = torch.tensor(node_features, dtype=torch.float)
    edge_index = torch.tensor(edge_index, dtype=torch.long)
    edge_attr = torch.tensor(edge_weights, dtype=torch.float).unsqueeze(1)
    
    return Data(x=x, edge_index=edge_index, edge_attr=edge_attr)

class MultiModalDataset(Dataset):
    """
    多模态数据集：同时加载结构数据（灰质体积图）和功能数据（功能连接）
    """
    def __init__(self, structural_transform=None, connectivity_threshold=0.3):
        self.structural_transform = structural_transform
        self.connectivity_threshold = connectivity_threshold
        self.data_pairs = []
        
        # 收集所有数据对
        self._collect_data_pairs()
    
    def _collect_data_pairs(self):
        """收集所有有效的数据对"""
        # 获取结构数据文件
        hc_structural_files = glob.glob('data_smri/HC/*.nii')
        mci_structural_files = glob.glob('data_smri/MCI/*.nii')
        
        # 处理HC组
        for struct_file in hc_structural_files:
            subject_id = extract_subject_id_from_path(struct_file, 'structural')
            if subject_id:
                # 检查是否存在对应的功能数据
                timeseries_file = f'data_fmri/selected_timeseries/HC/ROISignals_{subject_id}_selected.txt'
                matrix_file = f'data_fmri/subject_matrices/HC/ROICorrelation_FisherZ_{subject_id}_submatrix.txt'
                
                if os.path.exists(timeseries_file) and os.path.exists(matrix_file):
                    self.data_pairs.append({
                        'subject_id': subject_id,
                        'structural_path': struct_file,
                        'timeseries_path': timeseries_file,
                        'matrix_path': matrix_file,
                        'label': 0  # HC
                    })
        
        # 处理MCI组
        for struct_file in mci_structural_files:
            subject_id = extract_subject_id_from_path(struct_file, 'structural')
            if subject_id:
                # 检查是否存在对应的功能数据
                timeseries_file = f'data_fmri/selected_timeseries/MCI/ROISignals_{subject_id}_selected.txt'
                matrix_file = f'data_fmri/subject_matrices/MCI/ROICorrelation_FisherZ_{subject_id}_submatrix.txt'
                
                if os.path.exists(timeseries_file) and os.path.exists(matrix_file):
                    self.data_pairs.append({
                        'subject_id': subject_id,
                        'structural_path': struct_file,
                        'timeseries_path': timeseries_file,
                        'matrix_path': matrix_file,
                        'label': 1  # MCI
                    })
        
        print(f"找到 {len(self.data_pairs)} 个有效的多模态数据对")
        hc_count = sum(1 for pair in self.data_pairs if pair['label'] == 0)
        mci_count = sum(1 for pair in self.data_pairs if pair['label'] == 1)
        print(f"HC: {hc_count}, MCI: {mci_count}")
    
    def __len__(self):
        return len(self.data_pairs)
    
    def __getitem__(self, idx):
        data_pair = self.data_pairs[idx]
        
        # 加载结构数据
        structural_img = nib.load(data_pair['structural_path'])
        structural_data = structural_img.get_fdata(dtype=np.float32)
        
        # 应用结构数据变换
        if self.structural_transform:
            structural_data = self.structural_transform(structural_data)
        
        # 加载功能数据
        node_features = load_functional_node_features(data_pair['subject_id'], data_pair['label'])
        connectivity_matrix = load_functional_connectivity_matrix(data_pair['subject_id'], data_pair['label'])
        
        if node_features is None or connectivity_matrix is None:
            # 如果功能数据加载失败，返回默认值
            num_nodes = 22  # 假设有22个脑区
            node_features = np.random.randn(num_nodes, 16)
            connectivity_matrix = np.random.randn(num_nodes, num_nodes)
        
        # 创建图数据
        graph_data = create_graph_from_connectivity(
            connectivity_matrix, 
            node_features, 
            self.connectivity_threshold
        )
        
        return {
            'structural_data': structural_data,
            'functional_data': graph_data,
            'label': data_pair['label'],
            'subject_id': data_pair['subject_id']
        }

def create_data_splits(dataset, train_ratio=0.8, val_ratio=0.1, test_ratio=0.1, random_state=42):
    """
    创建8:1:1的数据划分
    
    Args:
        dataset: 数据集
        train_ratio: 训练集比例
        val_ratio: 验证集比例
        test_ratio: 测试集比例
        random_state: 随机种子
    
    Returns:
        训练集、验证集、测试集的索引
    """
    # 获取标签
    labels = [dataset.data_pairs[i]['label'] for i in range(len(dataset))]
    indices = list(range(len(dataset)))
    
    # 首先分离出测试集
    train_val_indices, test_indices = train_test_split(
        indices, 
        test_size=test_ratio, 
        stratify=labels, 
        random_state=random_state
    )
    
    # 从剩余数据中分离训练集和验证集
    train_val_labels = [labels[i] for i in train_val_indices]
    val_size = val_ratio / (train_ratio + val_ratio)
    
    train_indices, val_indices = train_test_split(
        train_val_indices,
        test_size=val_size,
        stratify=train_val_labels,
        random_state=random_state
    )
    
    print(f"数据划分完成:")
    print(f"训练集: {len(train_indices)} 样本")
    print(f"验证集: {len(val_indices)} 样本")
    print(f"测试集: {len(test_indices)} 样本")
    
    return train_indices, val_indices, test_indices

if __name__ == "__main__":
    # 测试数据集
    set_seed(42)
    
    # 定义变换
    transform = Compose([
        add_channel_dim,
        ScaleIntensity(minv=0.0, maxv=1.0),
        Resize(spatial_size=(64, 128, 128)),
        ToTensor()
    ])
    
    # 创建数据集
    dataset = MultiModalDataset(structural_transform=transform)
    
    # 创建数据划分
    train_indices, val_indices, test_indices = create_data_splits(dataset)
    
    # 测试数据加载
    sample = dataset[0]
    print(f"结构数据形状: {sample['structural_data'].shape}")
    print(f"功能数据节点数: {sample['functional_data'].x.shape[0]}")
    print(f"功能数据特征维度: {sample['functional_data'].x.shape[1]}")
    print(f"功能数据边数: {sample['functional_data'].edge_index.shape[1]}")
    print(f"标签: {sample['label']}")
    print(f"被试ID: {sample['subject_id']}")
